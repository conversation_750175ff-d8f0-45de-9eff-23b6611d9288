<?php

namespace App\Http\Controllers\Api\Category;

use App\Http\Controllers\Controller;
use App\Http\Requests\Category\CategoryRequest;
use App\Http\Resources\CategoryResource;
use App\Models\Product\Category;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\ToArray;

class CategoryController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware("auth:sanctum");
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();

            if(get_class($user) != 'App\Models\Organization\Organization'){
                $this->check_user_organization($user, $request);
            }


            // Validate the request parameters
            $validatedData = $request->validate([
                'id' => 'integer|exists:categories,id',
                'name' => 'string|max:255',
                'is_default' => 'boolean',
                'status' => 'boolean',
                'paginate' => 'integer|min:1|max:255',
            ]);

            $categories = $user->categories();
            if ($request->filled("id")) {
                $categories->where("id", $request->get("id"));
            }

            if ($request->filled("organization_id")) {
                $categories->where("organization_id", $request->get("organization_id"));
            }

            if ($request->filled("name")) {
                $categories->where("name", "LIKE", "%" . $request->get("name") . "%");
            }

            if ($request->filled("is_default")) {
                $categories->where("is_default", $request->get("is_default"));
            }

            if ($request->filled("status")) {
                $categories->where("status", $request->get("status"));
            }

            $paginate = $request->filled("paginate") ? $request->get("paginate") : 255;

            // Get paginated data
            $paginatedCategories = $categories->orderBy('updated_at', 'desc')->paginate($paginate);

            // Prepare pagination details
            $pagination = [
                'current_page' => $paginatedCategories->currentPage(),
                'last_page' => $paginatedCategories->lastPage(),
                'per_page' => $paginatedCategories->perPage(),
                'total' => $paginatedCategories->total(),
            ];

            // Check if 'tree_structure' parameter is passed
            if ($request->filled('tree_structure') && $request->get('tree_structure')) {
                // Build tree structure
                $categoriesTree = $this->buildTree($categories->get());
                return $this->successResponse(
                    'Categories Tree Structure retrieved successfully',
                    $categoriesTree,
                    200,
                    null
                );
            } else {
                return $this->successResponse(
                    'Categories retrieved successfully',
                    CategoryResource::collection($paginatedCategories),
                    200,
                    $pagination
                );
            }
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve categories', $e->getCode() != 0 ? $e->getCode() : 500, $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CategoryRequest $request)
    {
        $validatedData = $request->validated();
        //encode response in validatedData before creating category
        $dataWithEncode = $validatedData;

        $category = $request->user()->categories()->create($dataWithEncode);

        // Create channel for the newly created category
        if (isset($validatedData['response'])) {
            //convert object to array after decoding
            $arrayResponse = (array)json_decode($validatedData['response']);

            $this->createChannel($category, $arrayResponse);
        }
        return response([
            'message' => 'Category created successfully',
            'category' => $category
        ]);
    }


    private function createChannel($category, array $response)
    {
        // Create the category channel and associate it with the category
        $category->category_channels()->create([
            'channel_id' => $response['channel_id'],
            'store_connect_type' => $response['type'],
            'store_connect_id' => $response['collection_id']
        ]);
    }
    /**
     * Display the specified resource.
     */
    public function show(Request $request, string $id)
    {
        $category = $request->user()->categories()->findOrFail($id);
        return response([
            'message' => 'Category retrieved successfully',
            'category' => $category
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CategoryRequest $request, string $id)
    {
        $validatedData = $request->validated();
        $category  = $request->user()->categories()->findOrFail($id);
        $category->update($validatedData);

        // Create channel for the newly created category
        if (isset($validatedData['response'])) {
            // Convert the response object to array after decoding
            $arrayResponse = json_decode($validatedData['response'], true);

            // Update channel for the category
            $this->updateChannel($category, $arrayResponse);
        }

        return response([
            'message' => 'Category updated successfully',
            'category' => $category
        ]);
    }

    private function updateChannel($category, array $response)
    {
        // Find the associated channel for the category (assuming each category has only one channel)
        $channel = $category->category_channels()->findOrFail($response['id']);
        if ($channel) {
            // Update the pivot fields directly
            $channel->update([
                'store_connect_type' => $response['type'],
                'store_connect_id' => $response['collection_id'],
            ]);
        }
    }


    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, string $id)
    {
        $request->user()->categories()->findOrFail($id)->delete();
        return response([
            'message' => 'Category deleted successfully'
        ]);
    }



    /**
     * Build a tree structure of categories.
     *
     * @param  \Illuminate\Support\Collection  $categories
     * @return array
     */
    private function buildTree($categories)
    {
        $grouped = $categories->groupBy('category_id');

        $buildFn = function ($parentId) use (&$buildFn, $grouped) {
            return $grouped->get($parentId, collect())->map(function ($category) use ($buildFn) {
                return [
                    'value' => $category->id,
                    'title' => $category->name,
                    'children' => $buildFn($category->id)
                ];
            })->toArray();
        };

        return $buildFn(null); // Start building from the root
    }
}
